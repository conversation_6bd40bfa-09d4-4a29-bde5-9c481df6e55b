plugins {
    id 'application'
    id 'airbyte-bulk-connector'
    id "io.airbyte.gradle.docker"
    id 'airbyte-connector-docker-convention'
}

airbyteBulkConnector {
    core = 'load'
    toolkits = ['load-object-storage', 'load-azure-blob-storage']
    cdk = '0.1.22'
}

application {
    mainClass = 'io.airbyte.integrations.destination.azure_blob_storage.AzureBlobStorageDestinationKt'

    applicationDefaultJvmArgs = [
            '-XX:+ExitOnOutOfMemoryError', '-XX:MaxRAMPercentage=75.0',
//            Uncomment to run locally:
//          '--add-opens', 'java.base/java.lang=ALL-UNNAMED'
//            Uncomment to enable remote profiling:
//            '-XX:NativeMemoryTracking=detail',
//            '-Djava.rmi.server.hostname=localhost',
//            '-Dcom.sun.management.jmxremote=true',
//            '-Dcom.sun.management.jmxremote.port=6000',
//            '-Dcom.sun.management.jmxremote.rmi.port=6000',
//            '-Dcom.sun.management.jmxremote.local.only=false',
//            '-Dcom.sun.management.jmxremote.authenticate=false',
//            '-Dcom.sun.management.jmxremote.ssl=false'
    ]
}

// Uncomment to run locally
//run {
//    standardInput = System.in
//}

// Exclude conflicting log4j-over-slf4j dependency
configurations.all {
        exclude group: "org.slf4j", module: "slf4j-reload4j"
}

// TODO figure out if we need these
// dependencies {

//     implementation 'com.azure:azure-storage-blob:12.20.2'
//     implementation 'org.apache.commons:commons-csv:1.4'

//     testImplementation 'org.apache.commons:commons-lang3:3.11'
//     testImplementation "org.testcontainers:junit-jupiter:1.17.5"
// }

dependencies {
    integrationTestImplementation 'org.testcontainers:azure:1.20.6'
    integrationTestImplementation 'io.mockk:mockk:1.13.12'
}
