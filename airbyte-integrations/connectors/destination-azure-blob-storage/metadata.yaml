data:
  ab_internal:
    ql: 300
    sl: 200
    requireVersionIncrementsInPullRequests: false
  connectorBuildOptions:
    baseImage: docker.io/airbyte/java-connector-base:2.0.1@sha256:ec89bd1a89e825514dd2fc8730ba299a3ae1544580a078df0e35c5202c2085b3
  connectorSubtype: file
  connectorTestSuitesOptions:
    - suite: unitTests
    - suite: integrationTests
      testSecrets:
        - fileName: config.json
          name: SECRET_DESTINATION-AZURE-BLOB-STORAGE__CREDS
          secretStore:
            alias: airbyte-connector-testing-secret-store
            type: GSM
        - fileName: config_sas.json
          name: SECRET_DESTINATION-AZURE-BLOB-STORAGE-SAS__CREDS
          secretStore:
            alias: airbyte-connector-testing-secret-store
            type: GSM
  connectorType: destination
  definitionId: b4c5d105-31fd-4817-96b6-cb923bfc04cb
  dockerImageTag: 1.1.0
  dockerRepository: airbyte/destination-azure-blob-storage
  documentationUrl: https://docs.airbyte.com/integrations/destinations/azure-blob-storage
  githubIssueLabel: destination-azure-blob-storage
  icon: azureblobstorage.svg
  license: ELv2
  name: Azure Blob Storage
  connectorIPCOptions:
    dataChannel:
      version: "0.0.1"
      supportedSerialization: ["JSONL", "PROTOBUF"]
      supportedTransport: ["SOCKET", "STDIO"]
  registryOverrides:
    cloud:
      enabled: true
    oss:
      enabled: true
  releaseStage: generally_available
  releases:
    rolloutConfiguration:
      enableProgressiveRollout: false
    breakingChanges:
      1.0.0:
        message: >
          'This version updates the connector's behavior to match other certified destinations' behavior.
          In particular, the `_airbyte_*` columns have been renamed, and files are now placed in
          `<container>/<stream namespace>/<stream name>/**` (previously, stream namespace was not used
          in the path).

          Some options have been renamed for clarity. Notably, the "spill size" option is now named
          "file split size"; additionally, this option is now correctly applied to CSV files (previously
          it only applied to JSONL files).

          This connector now writes "block" blobs, instead of "append" blobs. This means that you may
          assume that as soon as a blob shows up in your Azure blob container, it is ready for consumption.
          (Previously, with append blobs, the connector would first write an empty blob, and fill it
          gradually.)

          '
        upgradeDeadline: "2026-03-31"
  resourceRequirements:
    jobSpecific:
      - jobType: sync
        resourceRequirements:
          memory_limit: 1Gi
          memory_request: 1Gi
  supportLevel: certified
  tags:
    - language:java
  supportsFileTransfer: true
  supportsRefreshes: true
metadataSpecVersion: "1.0"
