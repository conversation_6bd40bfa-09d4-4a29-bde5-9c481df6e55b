plugins {
    id 'application'
    id 'airbyte-bulk-connector'
    id "io.airbyte.gradle.docker"
    id 'airbyte-connector-docker-convention'
}

airbyteBulkConnector {
    core = 'load'
    toolkits = ['load-gcs', 'load-db', 'load-s3']
    cdk = '0.1.19'
}

java {
    // TODO: rewrite code to avoid javac wornings in the first place
    compileJava {
        options.compilerArgs += "-Xlint:-this-escape"
    }
}

application {
    mainClass = 'io.airbyte.integrations.destination.bigquery.BigQueryDestinationKt'
    applicationDefaultJvmArgs = ['-XX:+ExitOnOutOfMemoryError', '-XX:MaxRAMPercentage=75.0',
                                 '-XX:NativeMemoryTracking=detail', '-XX:+UnlockDiagnosticVMOptions',
                                 '-XX:GCLockerRetryAllocationCount=100',
//            '-Djava.rmi.server.hostname=localhost',
//            '-Dcom.sun.management.jmxremote=true',
//            '-Dcom.sun.management.jmxremote.port=6000',
//            '-Dcom.sun.management.jmxremote.rmi.port=6000',
//            '-Dcom.sun.management.jmxremote.local.only=false',
//            '-Dcom.sun.management.jmxremote.authenticate=false',
//            '-Dcom.sun.management.jmxremote.ssl=false'
    ]
}

dependencies {
    // Ideally we would just rely on the bom version:
    // implementation 'com.google.cloud:google-cloud-bigquery'
    // but we're still waiting for google to cut another release.
    // so for now, use a specific bigquery version.
    // https://github.com/airbytehq/airbyte-internal-issues/issues/13662
    implementation 'com.google.cloud:google-cloud-bigquery:2.53.0'

    integrationTestImplementation 'com.google.cloud:google-cloud-bigquery'
    testImplementation "io.mockk:mockk:1.13.13"
}
