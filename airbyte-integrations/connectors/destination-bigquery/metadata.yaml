data:
  ab_internal:
    ql: 300
    sl: 300
    requireVersionIncrementsInPullRequests: false
  connectorSubtype: database
  connectorType: destination
  definitionId: 22f6c74f-5699-40ff-833c-4a879ea40133
  dockerImageTag: 3.0.7
  dockerRepository: airbyte/destination-bigquery
  documentationUrl: https://docs.airbyte.com/integrations/destinations/bigquery
  githubIssueLabel: destination-bigquery
  icon: bigquery.svg
  license: ELv2
  name: BigQuery
  connectorBuildOptions:
    baseImage: docker.io/airbyte/java-connector-base:2.0.1@sha256:ec89bd1a89e825514dd2fc8730ba299a3ae1544580a078df0e35c5202c2085b3
  connectorIPCOptions:
    dataChannel:
      version: "0.0.1"
      supportedSerialization: ["JSONL", "PROTOBUF"]
      supportedTransport: ["SOCKET", "STDIO"]
  registryOverrides:
    cloud:
      enabled: true
    oss:
      enabled: true
  releaseStage: generally_available
  releases:
    breakingChanges:
      2.0.0:
        message: "**Do not upgrade until you have run a test upgrade as outlined [here](https://docs.airbyte.com/release_notes/upgrading_to_destinations_v2/#testing-destinations-v2-for-a-single-connection)**.\nThis version introduces [Destinations V2](https://docs.airbyte.com/release_notes/upgrading_to_destinations_v2/#what-is-destinations-v2), which provides better error handling, incremental delivery of data for large syncs, and improved final table structures. To review the breaking changes, and how to upgrade, see [here](https://docs.airbyte.com/release_notes/upgrading_to_destinations_v2/#quick-start-to-upgrading). These changes will likely require updates to downstream dbt / SQL models, which we walk through [here](https://docs.airbyte.com/release_notes/upgrading_to_destinations_v2/#updating-downstream-transformations).\nSelecting `Upgrade` will upgrade **all** connections using this destination at their next sync. You can manually sync existing connections prior to the next scheduled sync to start the upgrade early.\n"
        upgradeDeadline: "2023-11-07"
      3.0.0:
        message: "If you never interact with the raw tables, you can upgrade without taking any action. Otherwise, make sure to read the migration guide for more details."
        upgradeDeadline: "2026-07-31"
    rolloutConfiguration:
      enableProgressiveRollout: false
  resourceRequirements:
    jobSpecific:
      - jobType: sync
        resourceRequirements:
          memory_limit: 1Gi
          memory_request: 1Gi
  supportLevel: certified
  supportsDbt: true
  supportsRefreshes: true
  tags:
    - language:java
  connectorTestSuitesOptions:
    - suite: unitTests
    - suite: integrationTests
      testSecrets:
        - name: SECRET_DESTINATION-BIGQUERY_CREDENTIALS_1S1T_GCS
          fileName: credentials-1s1t-gcs.json
          secretStore:
            type: GSM
            alias: airbyte-connector-testing-secret-store
        - name: SECRET_DESTINATION-BIGQUERY_CREDENTIALS_1S1T_STANDARD
          fileName: credentials-1s1t-standard.json
          secretStore:
            type: GSM
            alias: airbyte-connector-testing-secret-store
        - name: SECRET_DESTINATION-BIGQUERY_CREDENTIALS_BAD_PROJECT_CREDS
          fileName: credentials-badproject.json
          secretStore:
            type: GSM
            alias: airbyte-connector-testing-secret-store
        - name: SECRET_DESTINATION-BIGQUERY_CREDENTIALS_NO_PUBLIC_SCHEMA_EDIT_ROLE
          fileName: credentials-no-edit-public-schema-role.json
          secretStore:
            type: GSM
            alias: airbyte-connector-testing-secret-store
        - name: SECRET_DESTINATION-BIGQUERY_STANDARD-NO-DATASET-CREATION__CREDS
          fileName: credentials-standard-no-dataset-creation.json
          secretStore:
            type: GSM
            alias: airbyte-connector-testing-secret-store
        - name: SECRET_DESTINATION-BIGQUERY_CREDENTIALS_1S1T_GCS_BAD_COPY
          fileName: credentials-1s1t-gcs-bad-copy-permission.json
          secretStore:
            type: GSM
            alias: airbyte-connector-testing-secret-store
metadataSpecVersion: "1.0"
