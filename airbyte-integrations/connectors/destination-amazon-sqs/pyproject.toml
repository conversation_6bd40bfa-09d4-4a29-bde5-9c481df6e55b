[build-system]
requires = [ "poetry-core>=1.0.0",]
build-backend = "poetry.core.masonry.api"

[tool.poetry]
version = "0.1.17"
name = "destination-amazon-sqs"
description = "Destination implementation for Amazon Sqs."
authors = [ "Airbyte <<EMAIL>>",]
license = "MIT"
readme = "README.md"
documentation = "https://docs.airbyte.com/integrations/destinations/amazon-sqs"
homepage = "https://airbyte.com"
repository = "https://github.com/airbytehq/airbyte"
[[tool.poetry.packages]]
include = "destination_amazon_sqs"

[tool.poetry.dependencies]
python = "^3.9,<3.12"
boto3 = "==1.34.56"
airbyte-cdk = "==0.68.1"

[tool.poetry.scripts]
destination-amazon-sqs = "destination_amazon_sqs.run:run"

[tool.poetry.group.dev.dependencies]
moto = "^5.0.7"
pytest = "^8.3.2"


[tool.poe]
include = [
    # Shared tasks definition file(s) can be imported here.
    # Run `poe` or `poe --help` to see the list of available tasks.
    "${POE_GIT_DIR}/poe-tasks/poetry-connector-tasks.toml",
]
