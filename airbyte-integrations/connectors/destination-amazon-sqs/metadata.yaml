data:
  connectorSubtype: api
  connectorType: destination
  definitionId: 0eeee7fb-518f-4045-bacc-9619e31c43ea
  dockerImageTag: 0.1.17
  dockerRepository: airbyte/destination-amazon-sqs
  githubIssueLabel: destination-amazon-sqs
  icon: awssqs.svg
  license: ELv2
  name: Amazon SQS
  registryOverrides:
    cloud:
      enabled: false
    oss:
      enabled: false
  releaseStage: alpha
  documentationUrl: https://docs.airbyte.com/integrations/destinations/amazon-sqs
  tags:
    - language:python
    - cdk:python
  ab_internal:
    sl: 100
    ql: 200
  supportLevel: community
  connectorTestSuitesOptions:
    # Disable unit tests for now
    # They are not passing
    # No Airbyte Cloud usage
    #- suite: unitTests
    - suite: integrationTests
      testSecrets:
        - name: SECRET_DESTINATION-AMAZON-SQS
          fileName: config.json
          secretStore:
            type: GSM
            alias: airbyte-connector-testing-secret-store
  connectorBuildOptions:
    baseImage: docker.io/airbyte/python-connector-base:2.0.0@sha256:c44839ba84406116e8ba68722a0f30e8f6e7056c726f447681bb9e9ece8bd916
metadataSpecVersion: "1.0"
