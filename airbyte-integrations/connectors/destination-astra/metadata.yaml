data:
  allowedHosts:
    hosts:
      - "*.apps.astra.datastax.com"
  registryOverrides:
    oss:
      enabled: true
    cloud:
      enabled: true
  connectorBuildOptions:
    # Please update to the latest version of the connector base image.
    # Please use the full address with sha256 hash to guarantee build reproducibility.
    # https://hub.docker.com/r/airbyte/python-connector-base
    baseImage: docker.io/airbyte/python-connector-base:4.0.0@sha256:d9894b6895923b379f3006fa251147806919c62b7d9021b5cd125bb67d7bbe22
  connectorSubtype: database
  connectorType: destination
  definitionId: 042ce96f-1158-4662-9543-e2ff015be97a
  dockerImageTag: 0.1.44
  dockerRepository: airbyte/destination-astra
  githubIssueLabel: destination-astra
  icon: astra.svg
  license: ELv2
  name: Astra DB
  releaseDate: 2024-01-10
  releaseStage: alpha
  supportLevel: community
  documentationUrl: https://docs.airbyte.com/integrations/destinations/astra
  tags:
    - language:python
    - cdk:python
  connectorTestSuitesOptions:
    - suite: unitTests
    - suite: integrationTests
      testSecrets:
        - name: SECRET_DESTINATION-ASTRA__CREDS
          fileName: config.json
          secretStore:
            type: GSM
            alias: airbyte-connector-testing-secret-store
    - suite: acceptanceTests
      testSecrets:
        - name: SECRET_DESTINATION-ASTRA__CREDS
          fileName: config.json
          secretStore:
            type: GSM
            alias: airbyte-connector-testing-secret-store
metadataSpecVersion: "1.0"
