// column renamings:
// * $starts_with_dollar_sign -> _starts_with_dollar_sign
// * includes"doublequote -> includes_doublequote TODO: This doesn't work in Databricks
// * includes'singlequote -> includes_singlequote
// * includes`backtick -> includes_backtick
// * includes$$doubledollar -> includes__doubledollar
// * includes.period -> includes_period
// * endswithbackslash\ -> endswithbackslash_
{"_airbyte_raw_id": "7e7330a1-42fb-41ec-a955-52f18bd61964", "_airbyte_extracted_at": "2023-01-01T00:00:00.000000", "_airbyte_meta": {"changes": []}, "id1": 1, "id2": 100, "updated_at": "2023-01-01T02:00:00.000000", "_starts_with_dollar_sign": "foo", "includes_backtick": "foo", "includes_singlequote": "foo", "includes_period": "foo", "includes__doubledollar": "foo", "endswithbackslash_": "foo"}
