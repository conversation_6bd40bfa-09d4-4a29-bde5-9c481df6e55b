{"_airbyte_extracted_at": "1970-01-01T00:00:01", "_airbyte_data": {"id1": 1, "id2": 200, "updated_at": "2000-01-01T00:00:00Z", "_ab_cdc_deleted_at": null, "name": "<PERSON>", "address": {"city": "San Francisco", "state": "CA"}}, "_airbyte_generation_id": 43, "_airbyte_meta": "{\"changes\":[],\"sync_id\":42}"}
{"_airbyte_extracted_at": "1970-01-01T00:00:01", "_airbyte_data": {"id1": 1, "id2": 200, "updated_at": "2000-01-01T00:01:00Z", "_ab_cdc_deleted_at": null, "name": "Alice", "address": {"city": "Los Angeles", "state": "CA"}}, "_airbyte_generation_id": 43, "_airbyte_meta": "{\"changes\":[],\"sync_id\":42}"}
{"_airbyte_extracted_at": "1970-01-01T00:00:01", "_airbyte_data": {"id1": 1, "id2": 201, "updated_at": "2000-01-01T00:02:00Z", "name": "Bob", "address": {"city": "Boston", "state": "MA"}}, "_airbyte_generation_id": 43, "_airbyte_meta": "{\"changes\":[],\"sync_id\":42}"}
// Invalid data is still allowed in the raw table.
{"_airbyte_extracted_at": "1970-01-01T00:00:01", "_airbyte_data": {"id1": 2, "id2": 200, "updated_at": "2000-01-01T00:03:00Z", "name": "Charlie", "age": 42, "registration_date": "2023-12-23"}, "_airbyte_generation_id": 43, "_airbyte_meta": "{\"sync_id\":42,\"changes\":[{\"field\":\"address\",\"change\":\"NULLED\",\"reason\":\"SOURCE_RETRIEVAL_ERROR\"}]}"}
{"_airbyte_extracted_at": "1970-01-01T00:00:01", "_airbyte_data": {"id1": 3, "id2": 200, "updated_at": "2000-01-01T00:04:00Z", "name": "a\bb\fc\nd\re\tf`~!@#$%^&*()_+-=[]\\{}|'\",./<>?"}, "_airbyte_generation_id": 43, "_airbyte_meta": "{\"changes\":[],\"sync_id\":42}"}
