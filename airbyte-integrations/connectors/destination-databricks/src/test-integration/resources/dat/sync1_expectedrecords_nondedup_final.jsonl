{"_airbyte_extracted_at": "1970-01-01T00:00:01", "_airbyte_generation_id": 43, "_airbyte_meta": "{\"sync_id\":42,\"changes\":[]}", "id1": 1, "id2": 200, "updated_at": "2000-01-01T00:00:00.000000", "name": "<PERSON>", "address": "{\"city\":\"San Francisco\",\"state\":\"CA\"}"}
{"_airbyte_extracted_at": "1970-01-01T00:00:01", "_airbyte_generation_id": 43, "_airbyte_meta": "{\"sync_id\":42,\"changes\":[]}", "id1": 1, "id2": 200, "updated_at": "2000-01-01T00:01:00.000000", "name": "<PERSON>", "address": "{\"city\":\"Los Angeles\",\"state\":\"CA\"}"}
{"_airbyte_extracted_at": "1970-01-01T00:00:01", "_airbyte_generation_id": 43, "_airbyte_meta": "{\"sync_id\":42,\"changes\":[]}", "id1": 1, "id2": 201, "updated_at": "2000-01-01T00:02:00.000000", "name": "Bob", "address": "{\"city\":\"Boston\",\"state\":\"MA\"}"}
// Invalid columns are nulled out (i.e. SQL null, not JSON null)
{"_airbyte_extracted_at": "1970-01-01T00:00:01", "_airbyte_generation_id": 43, "_airbyte_meta": "{\"sync_id\":42,\"changes\":[{\"field\":\"address\",\"change\":\"NULLED\",\"reason\":\"SOURCE_RETRIEVAL_ERROR\"}]}", "id1": 2, "id2": 200, "updated_at": "2000-01-01T00:03:00.000000", "name": "Charlie", "age": 42, "registration_date": "2023-12-23"}
{"_airbyte_extracted_at": "1970-01-01T00:00:01", "_airbyte_generation_id": 43, "_airbyte_meta": "{\"sync_id\":42,\"changes\":[]}", "id1": 3, "id2": 200, "updated_at": "2000-01-01T00:04:00.000000", "name": "a\bb\fc\nd\re\tf`~!@#$%^&*()_+-=[]\\{}|'\",./<>?"}
