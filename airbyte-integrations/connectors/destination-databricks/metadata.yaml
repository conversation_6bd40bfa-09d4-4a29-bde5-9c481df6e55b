data:
  connectorSubtype: database
  connectorType: destination
  definitionId: 072d5540-f236-4294-ba7c-ade8fd918496
  dockerImageTag: 3.3.7
  dockerRepository: airbyte/destination-databricks
  githubIssueLabel: destination-databricks
  icon: databricks.svg
  license: ELv2
  name: Databricks Lakehouse
  connectorBuildOptions:
    baseImage: docker.io/airbyte/java-connector-base:2.0.1@sha256:ec89bd1a89e825514dd2fc8730ba299a3ae1544580a078df0e35c5202c2085b3
  registryOverrides:
    cloud:
      enabled: true
    oss:
      enabled: true
  releaseStage: alpha
  releases:
    breakingChanges:
      2.0.0:
        message: >
          **This is a private preview version, Do not upgrade until you review the changes**.\n
          This version is a rewrite of the community connector with support for Unity catalog, and staging files using Unity catalog volumes.
          This version also introduces [Destinations V2](https://docs.airbyte.com/release_notes/upgrading_to_destinations_v2/#what-is-destinations-v2),
          which provides better error handling, incremental delivery of data for large syncs, and improved final table structures.
          To review the breaking changes, see [here](https://docs.airbyte.com/release_notes/upgrading_to_destinations_v2/#quick-start-to-upgrading).
        upgradeDeadline: "2025-01-31"
      3.0.0:
        message: >
          This version adds an `_airbyte_generation_id` column to the raw and final tables. If you ran a sync using 2.0.0, you MUST manually drop the
          raw and final tables and then clear (reset) your connection; this release will not automatically upgrade your tables.
        upgradeDeadline: "2025-01-31"
  documentationUrl: https://docs.airbyte.com/integrations/destinations/databricks
  resourceRequirements:
    jobSpecific:
      - jobType: check_connection
        resourceRequirements:
          memory_limit: 600Mi
          memory_request: 600Mi
  tags:
    - language:java
  ab_internal:
    sl: 100
    ql: 100
  supportLevel: certified
  supportsRefreshes: true
  connectorTestSuitesOptions:
    - suite: unitTests
    - suite: integrationTests
      testSecrets:
        - name: SECRET_DESTINATION-DATABRICKS_AZURE__CREDS
          fileName: azure_config.json
          secretStore:
            type: GSM
            alias: airbyte-connector-testing-secret-store
        - name: SECRET_DESTINATION-DATABRICKS__CREDS
          fileName: config.json
          secretStore:
            type: GSM
            alias: airbyte-connector-testing-secret-store
        - name: SECRET_DESTINATION-DATABRICKS__STAGING_CREDS
          fileName: staging_config.json
          secretStore:
            type: GSM
            alias: airbyte-connector-testing-secret-store
        - name: SECRET_DESTINATION_DATABRICKS_OAUTH_CONFIG
          fileName: oauth_config.json
          secretStore:
            type: GSM
            alias: airbyte-connector-testing-secret-store
        - name: SECRET_DESTINATION_DATABRICKS_PERSONAL_ACCESS_TOKEN_CONFIG
          fileName: pat_config.json
          secretStore:
            type: GSM
            alias: airbyte-connector-testing-secret-store
metadataSpecVersion: "1.0"
