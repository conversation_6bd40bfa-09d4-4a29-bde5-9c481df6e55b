[build-system]
requires = [ "poetry-core>=1.0.0",]
build-backend = "poetry.core.masonry.api"

[tool.poetry]
version = "0.1.58"
name = "destination-aws-datalake"
description = "Destination Implementation for AWS Datalake."
authors = [ "Airbyte <<EMAIL>>",]
license = "MIT"
readme = "README.md"
documentation = "https://docs.airbyte.com/integrations/destination/aws-datalake"
homepage = "https://airbyte.com"
repository = "https://github.com/airbytehq/airbyte"
[[tool.poetry.packages]]
include = "destination_aws_datalake"

[tool.poetry.dependencies]
python = "^3.9,<3.12"
airbyte-cdk = "==0.84.0"
retrying = "^1"
awswrangler = "==3.7.1"
pandas = "==2.0.3"

[tool.poetry.scripts]
destination-aws-datalake = "destination_aws_datalake.run:run"

[tool.poetry.group.dev.dependencies]
pytest = "^8.3.2"


[tool.poe]
include = [
    # Shared tasks definition file(s) can be imported here.
    # Run `poe` or `poe --help` to see the list of available tasks.
    "${POE_GIT_DIR}/poe-tasks/poetry-connector-tasks.toml",
]
