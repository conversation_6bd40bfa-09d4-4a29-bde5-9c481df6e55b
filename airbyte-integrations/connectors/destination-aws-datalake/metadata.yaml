data:
  connectorSubtype: database
  connectorType: destination
  definitionId: 99878c90-0fbd-46d3-9d98-ffde879d17fc
  connectorBuildOptions:
    baseImage: docker.io/airbyte/python-connector-base:4.0.0@sha256:d9894b6895923b379f3006fa251147806919c62b7d9021b5cd125bb67d7bbe22
  dockerImageTag: 0.1.58
  dockerRepository: airbyte/destination-aws-datalake
  githubIssueLabel: destination-aws-datalake
  icon: awsdatalake.svg
  license: ELv2
  name: AWS Datalake
  registryOverrides:
    cloud:
      enabled: true
    oss:
      enabled: true
  releaseStage: alpha
  documentationUrl: https://docs.airbyte.com/integrations/destinations/aws-datalake
  tags:
    - language:python
    - cdk:python
  ab_internal:
    sl: 100
    ql: 100
  supportLevel: community
  connectorTestSuitesOptions:
    - suite: unitTests
    - suite: integrationTests
      testSecrets:
        - name: SECRET_DESTINATION-AWS-DATALAKE_CREDS
          fileName: config.json
          secretStore:
            type: GSM
            alias: airbyte-connector-testing-secret-store
metadataSpecVersion: "1.0"
