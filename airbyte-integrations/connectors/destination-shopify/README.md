# Destination Shopify

This is the repository for the Shopify destination connector in Python.
For information about how to use this connector within Airbyte, see [the User Documentation](https://docs.airbyte.com/integrations/destinations/shopify).

## Overview

The Shopify destination connector allows you to sync data from various sources to your Shopify store. It's particularly useful for:

- Syncing product data from databases (PostgreSQL, MySQL, etc.) to Shopify
- Uploading product images and videos from URLs
- Bulk product creation and updates
- Configurable field mapping between source and Shopify fields

### Key Features

- **Product Management**: Create and update products with variants, pricing, and inventory
- **Media Upload**: Upload images and videos from URLs to Shopify
- **Field Mapping**: Configurable mapping between source fields and Shopify product fields
- **Batch Processing**: Efficient batch processing for large datasets
- **Error Handling**: Comprehensive error handling and retry logic
- **Authentication**: Support for OAuth 2.0 and API password authentication

### Supported Sync Modes

- **Full Refresh - Overwrite**: Replaces all existing data
- **Full Refresh - Append**: Adds new data without removing existing data
- **Incremental**: Syncs only new or updated records (coming soon)

## Configuration

### Required Fields

- **Shop**: Your Shopify store name (e.g., "my-store" or "my-store.myshopify.com")
- **Credentials**: Authentication method (OAuth 2.0 or API Password)

### Field Mapping Configuration

Configure how your source fields map to Shopify product fields:

- **Product Title Field**: Source field for product title (required)
- **Product Description Field**: Source field for product description
- **Product Vendor Field**: Source field for product vendor/brand
- **Product Type Field**: Source field for product type/category
- **Images Field**: Source field containing image URLs (comma-separated or JSON array)
- **Videos Field**: Source field containing video URLs (comma-separated or JSON array)
- **Price Field**: Source field for product price
- **SKU Field**: Source field for product SKU
- **Inventory Quantity Field**: Source field for inventory quantity
- **Tags Field**: Source field for product tags (comma-separated or JSON array)

### Media Upload Settings

- **Upload Images**: Enable/disable image upload
- **Upload Videos**: Enable/disable video upload
- **Max Image Size**: Maximum image file size in MB (1-100)
- **Max Video Size**: Maximum video file size in MB (1-5120)
- **Supported Formats**: Configure supported file formats

### Sync Settings

- **Batch Size**: Number of products to process in each batch (1-100)
- **Update Existing Products**: Whether to update existing products or only create new ones
- **Product Match Field**: Field to use for matching existing products (sku, title, or handle)

## Local development

### Create credentials

**If you are a community contributor**, follow the instructions in the [documentation](https://docs.airbyte.com/integrations/destinations/shopify)
to generate the necessary credentials. Then create a file `secrets/config.json` conforming to the `destination_shopify/spec.json` file.
Note that any directory named `secrets` is gitignored across the entire Airbyte repo, so there is no danger of accidentally checking in sensitive information.
See `sample_files/sample_config.json` for a sample config file.

### Locally running the connector

```
poetry run destination-shopify spec
poetry run destination-shopify check --config secrets/config.json
poetry run destination-shopify write --config secrets/config.json --catalog integration_tests/configured_catalog.json
```

### Locally running the connector docker image

#### Build

First, make sure you build the latest Docker image:

```
docker build . -t airbyte/destination-shopify:dev
```

You can also build the connector image via Gradle:

```
./gradlew :airbyte-integrations:connectors:destination-shopify:buildConnectorImage
```

#### Run

Then run any of the connector commands as follows:

```
docker run --rm airbyte/destination-shopify:dev spec
docker run --rm -v $(pwd)/secrets:/secrets airbyte/destination-shopify:dev check --config /secrets/config.json
docker run --rm -v $(pwd)/secrets:/secrets -v $(pwd)/integration_tests:/integration_tests airbyte/destination-shopify:dev write --config /secrets/config.json --catalog /integration_tests/configured_catalog.json
```

## Testing

Make sure to familiarize yourself with [pytest test discovery](https://docs.pytest.org/en/latest/goodpractices.html#test-discovery) to know how your test files and methods should be named.
First install test dependencies into your virtual environment:

```
pip install .[tests]
```

### Unit Tests

To run unit tests locally, from the connector directory run:

```
poetry run pytest unit_tests
```

### Integration Tests

There are two types of integration tests: Acceptance Tests (Airbyte's test suite for all destination connectors) and custom integration tests (which are specific to this connector).

#### Custom Integration tests

Place custom tests inside `integration_tests/` folder, then, from the connector directory, run

```
poetry run pytest integration_tests
```

#### Acceptance Tests

Customize `acceptance-test-config.yml` file to configure tests. See [Connector Acceptance Tests](https://docs.airbyte.com/connector-development/testing-connectors/connector-acceptance-tests-reference) for more information.
If your connector requires to create or destroy resources for use during acceptance tests create fixtures for it and place them inside integration_tests/acceptance.py.
To run your integration tests with acceptance tests, from the connector directory, run

```
poetry run pytest integration_tests -v
```

## Usage Examples

### Basic Product Sync

Here's an example of syncing product data from a PostgreSQL database to Shopify:

1. **Source Data Structure** (PostgreSQL table):

```sql
CREATE TABLE products (
    id SERIAL PRIMARY KEY,
    name VARCHAR(255),
    description TEXT,
    brand VARCHAR(100),
    category VARCHAR(100),
    price DECIMAL(10,2),
    sku VARCHAR(50),
    stock_quantity INTEGER,
    image_urls TEXT,
    video_urls TEXT,
    tags TEXT
);
```

2. **Destination Configuration**:

```json
{
  "shop": "my-store.myshopify.com",
  "credentials": {
    "auth_method": "oauth2.0",
    "access_token": "your_access_token"
  },
  "field_mapping": {
    "product_title_field": "name",
    "product_description_field": "description",
    "product_vendor_field": "brand",
    "product_type_field": "category",
    "price_field": "price",
    "sku_field": "sku",
    "inventory_quantity_field": "stock_quantity",
    "images_field": "image_urls",
    "videos_field": "video_urls",
    "tags_field": "tags"
  }
}
```

3. **Sample Data**:

```json
{
  "name": "Wireless Headphones",
  "description": "High-quality wireless headphones with noise cancellation",
  "brand": "TechBrand",
  "category": "Electronics",
  "price": "199.99",
  "sku": "WH-001",
  "stock_quantity": 50,
  "image_urls": "https://example.com/headphones1.jpg,https://example.com/headphones2.jpg",
  "video_urls": "https://example.com/headphones-demo.mp4",
  "tags": "wireless,audio,electronics,noise-cancelling"
}
```

### Advanced Configuration

For more complex scenarios, you can configure additional settings:

```json
{
  "shop": "my-store.myshopify.com",
  "credentials": {
    "auth_method": "oauth2.0",
    "access_token": "your_access_token"
  },
  "field_mapping": {
    "product_title_field": "product.name",
    "product_description_field": "product.description",
    "images_field": "media.images",
    "videos_field": "media.videos"
  },
  "media_upload_settings": {
    "upload_images": true,
    "upload_videos": true,
    "max_image_size_mb": 10,
    "max_video_size_mb": 500,
    "supported_image_formats": ["jpg", "png", "webp"],
    "supported_video_formats": ["mp4", "webm"]
  },
  "sync_settings": {
    "batch_size": 5,
    "update_existing_products": true,
    "product_match_field": "sku"
  }
}
```

## Troubleshooting

### Common Issues

1. **Authentication Errors**

   - Verify your access token has the required permissions
   - Check that your shop URL is correct
   - Ensure your app has the necessary scopes (products, media)

2. **Media Upload Failures**

   - Verify image/video URLs are publicly accessible
   - Check file sizes against configured limits
   - Ensure file formats are supported

3. **Product Creation Failures**
   - Verify required fields (title) are mapped correctly
   - Check for duplicate SKUs if using SKU matching
   - Validate price format (should be string with decimal)

### Performance Tips

- Use appropriate batch sizes (5-20 for products with media, 50-100 for simple products)
- Consider disabling media upload for initial bulk imports
- Use SKU matching for updates to avoid duplicates
- Monitor Shopify API rate limits

## Limitations

- Maximum 100 products per batch
- Image files up to 100MB each
- Video files up to 5GB each
- Shopify API rate limits apply (2 requests per second for REST API)
- GraphQL mutations have their own rate limits

## Contributing

We welcome contributions! Please see our [contributing guide](../../CONTRIBUTING.md) for details.
