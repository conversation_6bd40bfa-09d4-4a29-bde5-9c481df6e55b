#
# Copyright (c) 2023 Airbyte, Inc., all rights reserved.
#

import pytest
from destination_shopify.field_mapper import FieldMapper


class TestFieldMapper:
    
    def test_map_record_to_product_basic(self):
        """Test basic record to product mapping"""
        config = {
            "product_title_field": "name",
            "product_description_field": "desc",
            "product_vendor_field": "brand",
            "price_field": "cost",
            "sku_field": "product_sku"
        }
        
        mapper = FieldMapper(config)
        
        record = {
            "name": "Test Product",
            "desc": "A test product",
            "brand": "Test Brand",
            "cost": "29.99",
            "product_sku": "TEST-001"
        }
        
        result = mapper.map_record_to_product(record)
        
        assert result["title"] == "Test Product"
        assert result["descriptionHtml"] == "A test product"
        assert result["vendor"] == "Test Brand"
        assert result["variants"][0]["price"] == "29.99"
        assert result["variants"][0]["sku"] == "TEST-001"

    def test_map_record_with_nested_fields(self):
        """Test mapping with nested field access"""
        config = {
            "product_title_field": "product.name",
            "price_field": "pricing.amount"
        }
        
        mapper = FieldMapper(config)
        
        record = {
            "product": {
                "name": "Nested Product"
            },
            "pricing": {
                "amount": "49.99"
            }
        }
        
        result = mapper.map_record_to_product(record)
        
        assert result["title"] == "Nested Product"
        assert result["variants"][0]["price"] == "49.99"

    def test_parse_tags_from_string(self):
        """Test parsing tags from comma-separated string"""
        config = {"tags_field": "tags"}
        mapper = FieldMapper(config)
        
        record = {"tags": "electronics, gadgets, mobile"}
        result = mapper.map_record_to_product(record)
        
        assert result["tags"] == ["electronics", "gadgets", "mobile"]

    def test_parse_tags_from_json_array(self):
        """Test parsing tags from JSON array string"""
        config = {"tags_field": "tags"}
        mapper = FieldMapper(config)
        
        record = {"tags": '["electronics", "gadgets", "mobile"]'}
        result = mapper.map_record_to_product(record)
        
        assert result["tags"] == ["electronics", "gadgets", "mobile"]

    def test_parse_tags_from_list(self):
        """Test parsing tags from list"""
        config = {"tags_field": "tags"}
        mapper = FieldMapper(config)
        
        record = {"tags": ["electronics", "gadgets", "mobile"]}
        result = mapper.map_record_to_product(record)
        
        assert result["tags"] == ["electronics", "gadgets", "mobile"]

    def test_get_media_files_from_string(self):
        """Test getting media files from comma-separated string"""
        config = {
            "images_field": "images",
            "videos_field": "videos"
        }
        mapper = FieldMapper(config)
        
        record = {
            "images": "http://example.com/img1.jpg, http://example.com/img2.png",
            "videos": "http://example.com/vid1.mp4"
        }
        
        result = mapper.get_media_files(record)
        
        assert len(result["images"]) == 2
        assert "http://example.com/img1.jpg" in result["images"]
        assert "http://example.com/img2.png" in result["images"]
        assert len(result["videos"]) == 1
        assert "http://example.com/vid1.mp4" in result["videos"]

    def test_get_media_files_from_json(self):
        """Test getting media files from JSON array"""
        config = {
            "images_field": "images"
        }
        mapper = FieldMapper(config)
        
        record = {
            "images": '["http://example.com/img1.jpg", "http://example.com/img2.png"]'
        }
        
        result = mapper.get_media_files(record)
        
        assert len(result["images"]) == 2
        assert "http://example.com/img1.jpg" in result["images"]
        assert "http://example.com/img2.png" in result["images"]

    def test_get_product_identifier_sku(self):
        """Test getting product identifier by SKU"""
        config = {"sku_field": "product_sku"}
        mapper = FieldMapper(config)
        
        record = {"product_sku": "TEST-001"}
        
        result = mapper.get_product_identifier(record, "sku")
        assert result == "TEST-001"

    def test_get_product_identifier_title(self):
        """Test getting product identifier by title"""
        config = {"product_title_field": "name"}
        mapper = FieldMapper(config)
        
        record = {"name": "Test Product"}
        
        result = mapper.get_product_identifier(record, "title")
        assert result == "Test Product"

    def test_get_product_identifier_handle(self):
        """Test getting product identifier by handle (generated from title)"""
        config = {"product_title_field": "name"}
        mapper = FieldMapper(config)
        
        record = {"name": "Test Product Name!"}
        
        result = mapper.get_product_identifier(record, "handle")
        assert result == "test-product-name"

    def test_validate_required_fields_success(self):
        """Test successful validation of required fields"""
        config = {"product_title_field": "name"}
        mapper = FieldMapper(config)
        
        record = {"name": "Test Product"}
        
        missing = mapper.validate_required_fields(record)
        assert len(missing) == 0

    def test_validate_required_fields_missing(self):
        """Test validation with missing required fields"""
        config = {"product_title_field": "name"}
        mapper = FieldMapper(config)
        
        record = {"description": "Test Description"}
        
        missing = mapper.validate_required_fields(record)
        assert "product_title_field" in missing

    def test_prepare_media_for_upload(self):
        """Test preparing media for upload"""
        config = {}
        mapper = FieldMapper(config)
        
        media_files = {
            "images": ["http://example.com/img1.jpg", "http://example.com/img2.png"],
            "videos": ["http://example.com/vid1.mp4"]
        }
        
        media_settings = {
            "upload_images": True,
            "upload_videos": True,
            "supported_image_formats": ["jpg", "png"],
            "supported_video_formats": ["mp4"]
        }
        
        result = mapper.prepare_media_for_upload(media_files, media_settings)
        
        assert len(result) == 3
        assert result[0]["mediaContentType"] == "IMAGE"
        assert result[1]["mediaContentType"] == "IMAGE"
        assert result[2]["mediaContentType"] == "VIDEO"

    def test_prepare_media_for_upload_disabled(self):
        """Test preparing media when upload is disabled"""
        config = {}
        mapper = FieldMapper(config)
        
        media_files = {
            "images": ["http://example.com/img1.jpg"],
            "videos": ["http://example.com/vid1.mp4"]
        }
        
        media_settings = {
            "upload_images": False,
            "upload_videos": False
        }
        
        result = mapper.prepare_media_for_upload(media_files, media_settings)
        
        assert len(result) == 0

    def test_is_valid_media_url_image(self):
        """Test image URL validation"""
        config = {}
        mapper = FieldMapper(config)
        
        settings = {"supported_image_formats": ["jpg", "png"]}
        
        assert mapper._is_valid_media_url("http://example.com/img.jpg", "image", settings)
        assert mapper._is_valid_media_url("https://example.com/img.png", "image", settings)
        assert not mapper._is_valid_media_url("http://example.com/img.gif", "image", settings)
        assert not mapper._is_valid_media_url("invalid-url", "image", settings)

    def test_is_valid_media_url_video(self):
        """Test video URL validation"""
        config = {}
        mapper = FieldMapper(config)
        
        settings = {"supported_video_formats": ["mp4", "mov"]}
        
        assert mapper._is_valid_media_url("http://example.com/vid.mp4", "video", settings)
        assert mapper._is_valid_media_url("https://example.com/vid.mov", "video", settings)
        assert not mapper._is_valid_media_url("http://example.com/vid.avi", "video", settings)
        assert not mapper._is_valid_media_url("invalid-url", "video", settings)
