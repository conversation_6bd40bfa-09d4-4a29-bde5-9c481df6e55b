#
# Copyright (c) 2023 Airbyte, Inc., all rights reserved.
#

import pytest
from unittest.mock import Mock, patch
from airbyte_cdk.models import AirbyteConnectionStatus, Status

from destination_shopify.destination import DestinationShopify


class TestDestinationShopify:
    
    def test_check_connection_success(self):
        """Test successful connection check"""
        config = {
            "shop": "test-store",
            "credentials": {
                "auth_method": "oauth2.0",
                "client_id": "test_client_id",
                "client_secret": "test_client_secret",
                "access_token": "test_access_token"
            },
            "field_mapping": {
                "product_title_field": "title"
            }
        }
        
        destination = DestinationShopify()
        
        with patch('destination_shopify.destination.ShopifyClient') as mock_client_class:
            mock_client = Mock()
            mock_client.test_connection.return_value = True
            mock_client.get_shop_info.return_value = {"name": "Test Store"}
            mock_client_class.return_value = mock_client
            
            result = destination.check(Mock(), config)
            
            assert result.status == Status.SUCCEEDED
            mock_client.test_connection.assert_called_once()
            mock_client.get_shop_info.assert_called_once()

    def test_check_connection_missing_shop(self):
        """Test connection check with missing shop"""
        config = {
            "credentials": {
                "auth_method": "oauth2.0",
                "access_token": "test_token"
            }
        }
        
        destination = DestinationShopify()
        result = destination.check(Mock(), config)
        
        assert result.status == Status.FAILED
        assert "Shop URL is required" in result.message

    def test_check_connection_missing_credentials(self):
        """Test connection check with missing credentials"""
        config = {
            "shop": "test-store"
        }
        
        destination = DestinationShopify()
        result = destination.check(Mock(), config)
        
        assert result.status == Status.FAILED
        assert "Credentials are required" in result.message

    def test_check_connection_invalid_auth_method(self):
        """Test connection check with invalid auth method"""
        config = {
            "shop": "test-store",
            "credentials": {
                "auth_method": "invalid_method"
            },
            "field_mapping": {
                "product_title_field": "title"
            }
        }
        
        destination = DestinationShopify()
        result = destination.check(Mock(), config)
        
        assert result.status == Status.FAILED
        assert "Unsupported authentication method" in result.message

    def test_check_connection_api_error(self):
        """Test connection check with API error"""
        config = {
            "shop": "test-store",
            "credentials": {
                "auth_method": "oauth2.0",
                "client_id": "test_client_id",
                "client_secret": "test_client_secret",
                "access_token": "test_access_token"
            },
            "field_mapping": {
                "product_title_field": "title"
            }
        }
        
        destination = DestinationShopify()
        
        with patch('destination_shopify.destination.ShopifyClient') as mock_client_class:
            mock_client = Mock()
            mock_client.test_connection.return_value = False
            mock_client_class.return_value = mock_client
            
            result = destination.check(Mock(), config)
            
            assert result.status == Status.FAILED
            assert "Failed to connect to Shopify" in result.message

    def test_validate_config_success(self):
        """Test successful config validation"""
        config = {
            "shop": "test-store",
            "credentials": {
                "auth_method": "oauth2.0",
                "client_id": "test_client_id",
                "client_secret": "test_client_secret",
                "access_token": "test_access_token"
            },
            "field_mapping": {
                "product_title_field": "title"
            },
            "sync_settings": {
                "batch_size": 10
            },
            "media_upload_settings": {
                "max_image_size_mb": 20,
                "max_video_size_mb": 1024
            }
        }
        
        destination = DestinationShopify()
        errors = destination._validate_config(config)
        
        assert len(errors) == 0

    def test_validate_config_missing_title_field(self):
        """Test config validation with missing title field"""
        config = {
            "shop": "test-store",
            "credentials": {
                "auth_method": "oauth2.0",
                "access_token": "test_token"
            },
            "field_mapping": {}
        }
        
        destination = DestinationShopify()
        errors = destination._validate_config(config)
        
        assert "Product title field mapping is required" in errors

    def test_validate_config_invalid_batch_size(self):
        """Test config validation with invalid batch size"""
        config = {
            "shop": "test-store",
            "credentials": {
                "auth_method": "oauth2.0",
                "access_token": "test_token"
            },
            "field_mapping": {
                "product_title_field": "title"
            },
            "sync_settings": {
                "batch_size": 150  # Too large
            }
        }
        
        destination = DestinationShopify()
        errors = destination._validate_config(config)
        
        assert any("Batch size must be an integer between 1 and 100" in error for error in errors)

    def test_get_shop_name(self):
        """Test shop name extraction"""
        # Test with full URL
        config1 = {"shop": "test-store.myshopify.com"}
        assert DestinationShopify.get_shop_name(config1) == "test-store"
        
        # Test with just shop name
        config2 = {"shop": "test-store"}
        assert DestinationShopify.get_shop_name(config2) == "test-store"
