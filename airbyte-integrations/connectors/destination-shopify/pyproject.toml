[build-system]
requires = [ "poetry-core>=1.0.0",]
build-backend = "poetry.core.masonry.api"

[tool.poetry]
version = "0.1.0"
name = "destination_shopify"
description = "Destination implementation for Shopify."
authors = [ "Airbyte <<EMAIL>>",]
license = "MIT"
readme = "README.md"
documentation = "https://docs.airbyte.com/integrations/destinations/shopify"
homepage = "https://airbyte.com"
repository = "https://github.com/airbytehq/airbyte"

[[tool.poetry.packages]]
include = "destination_shopify"

[tool.poetry.dependencies]
python = "^3.10,<3.12"
airbyte-cdk = "^6.22"
requests = "^2.31.0"

[tool.poetry.scripts]
destination-shopify = "destination_shopify.run:run"

[tool.poetry.group.dev.dependencies]
pytest = "^8.3.3"
pytest-mock = "^3.12.0"
requests-mock = "^1.11.0"

[tool.poe]
include = [
    # Shared tasks definition file(s) can be imported here.
    # Run `poe` or `poe --help` to see the list of available tasks.
    "${POE_GIT_DIR}/poe-tasks/poetry-connector-tasks.toml",
]
