data:
  ab_internal:
    ql: 100
    sl: 100
  allowedHosts:
    hosts:
      - ${shop}.myshopify.com
      - shopify.com
  connectorBuildOptions:
    baseImage: docker.io/airbyte/python-connector-base:4.0.0@sha256:d9894b6895923b379f3006fa251147806919c62b7d9021b5cd125bb67d7bbe22
  connectorSubtype: api
  connectorType: destination
  definitionId: 9da77001-af33-4bcd-be46-6252bf9342b0
  dockerImageTag: 0.1.0
  dockerRepository: airbyte/destination-shopify
  documentationUrl: https://docs.airbyte.com/integrations/destinations/shopify
  githubIssueLabel: destination-shopify
  icon: shopify.svg
  license: ELv2
  name: Shopify
  remoteRegistries:
    pypi:
      enabled: true
      packageName: airbyte-destination-shopify
  registryOverrides:
    cloud:
      enabled: true
    oss:
      enabled: true
  releaseStage: alpha
  supportLevel: community
  tags:
    - language:python
    - cdk:python
  connectorTestSuitesOptions:
    - suite: liveTests
      testConnections:
        - name: shopify_config_dev_null
          id: 1de7c985-bcce-425a-a762-57389cea3901
metadataSpecVersion: "1.0"
