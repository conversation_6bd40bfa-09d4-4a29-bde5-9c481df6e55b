{"streams": [{"stream": {"name": "products", "json_schema": {"type": "object", "properties": {"title": {"type": "string"}, "description": {"type": "string"}, "vendor": {"type": "string"}, "product_type": {"type": "string"}, "price": {"type": "string"}, "sku": {"type": "string"}, "inventory_quantity": {"type": "integer"}, "images": {"type": "string"}, "videos": {"type": "string"}, "tags": {"type": "string"}}}, "supported_sync_modes": ["full_refresh", "incremental"], "source_defined_cursor": false}, "sync_mode": "full_refresh", "destination_sync_mode": "overwrite"}]}