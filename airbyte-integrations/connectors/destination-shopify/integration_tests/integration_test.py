#
# Copyright (c) 2023 Airbyte, Inc., all rights reserved.
#

import json
import pytest
from unittest.mock import Mock, patch

from airbyte_cdk.models import AirbyteMessage, AirbyteRecordMessage, Type
from destination_shopify.destination import DestinationShopify


class TestShopifyIntegration:
    
    @pytest.fixture
    def config(self):
        """Sample configuration for testing"""
        return {
            "shop": "test-store",
            "credentials": {
                "auth_method": "oauth2.0",
                "client_id": "test_client_id",
                "client_secret": "test_client_secret",
                "access_token": "test_access_token"
            },
            "field_mapping": {
                "product_title_field": "title",
                "product_description_field": "description",
                "product_vendor_field": "vendor",
                "price_field": "price",
                "sku_field": "sku",
                "images_field": "images"
            },
            "media_upload_settings": {
                "upload_images": True,
                "upload_videos": False
            },
            "sync_settings": {
                "batch_size": 2,
                "update_existing_products": True,
                "product_match_field": "sku"
            }
        }

    @pytest.fixture
    def configured_catalog(self):
        """Sample configured catalog"""
        with open("integration_tests/configured_catalog.json", "r") as f:
            return json.load(f)

    def test_write_products_success(self, config, configured_catalog):
        """Test successful product writing"""
        destination = DestinationShopify()
        
        # Create test messages
        messages = [
            AirbyteMessage(
                type=Type.RECORD,
                record=AirbyteRecordMessage(
                    stream="products",
                    data={
                        "title": "Test Product 1",
                        "description": "A test product",
                        "vendor": "Test Vendor",
                        "price": "29.99",
                        "sku": "TEST-001",
                        "images": "http://example.com/img1.jpg"
                    },
                    emitted_at=1234567890
                )
            ),
            AirbyteMessage(
                type=Type.RECORD,
                record=AirbyteRecordMessage(
                    stream="products",
                    data={
                        "title": "Test Product 2",
                        "description": "Another test product",
                        "vendor": "Test Vendor",
                        "price": "39.99",
                        "sku": "TEST-002",
                        "images": "http://example.com/img2.jpg"
                    },
                    emitted_at=1234567891
                )
            )
        ]
        
        with patch('destination_shopify.destination.ShopifyClient') as mock_client_class:
            mock_client = Mock()
            mock_client.test_connection.return_value = True
            mock_client.get_shop_info.return_value = {"name": "Test Store"}
            mock_client.get_product_by_sku.return_value = None  # No existing products
            mock_client.create_product.return_value = {"id": "gid://shopify/Product/123", "title": "Test Product"}
            mock_client.upload_media_file.return_value = {"id": "gid://shopify/MediaImage/456"}
            mock_client.create_product_media.return_value = [{"id": "gid://shopify/ProductMedia/789"}]
            mock_client_class.return_value = mock_client
            
            # Execute write operation
            result_messages = list(destination.write(config, configured_catalog, messages))
            
            # Verify client calls
            assert mock_client.create_product.call_count == 2
            assert mock_client.upload_media_file.call_count == 2
            assert mock_client.create_product_media.call_count == 2

    def test_write_products_with_existing_update(self, config, configured_catalog):
        """Test updating existing products"""
        destination = DestinationShopify()
        
        messages = [
            AirbyteMessage(
                type=Type.RECORD,
                record=AirbyteRecordMessage(
                    stream="products",
                    data={
                        "title": "Updated Product",
                        "description": "An updated product",
                        "vendor": "Test Vendor",
                        "price": "49.99",
                        "sku": "EXISTING-001"
                    },
                    emitted_at=1234567890
                )
            )
        ]
        
        with patch('destination_shopify.destination.ShopifyClient') as mock_client_class:
            mock_client = Mock()
            mock_client.test_connection.return_value = True
            mock_client.get_shop_info.return_value = {"name": "Test Store"}
            mock_client.get_product_by_sku.return_value = {
                "id": "gid://shopify/Product/existing",
                "title": "Existing Product"
            }
            mock_client.update_product.return_value = {"id": "gid://shopify/Product/existing", "title": "Updated Product"}
            mock_client_class.return_value = mock_client
            
            # Execute write operation
            result_messages = list(destination.write(config, configured_catalog, messages))
            
            # Verify update was called instead of create
            mock_client.update_product.assert_called_once()
            mock_client.create_product.assert_not_called()

    def test_write_products_batch_processing(self, config, configured_catalog):
        """Test batch processing of products"""
        destination = DestinationShopify()
        
        # Create 5 messages to test batching (batch_size = 2)
        messages = []
        for i in range(5):
            messages.append(
                AirbyteMessage(
                    type=Type.RECORD,
                    record=AirbyteRecordMessage(
                        stream="products",
                        data={
                            "title": f"Test Product {i}",
                            "description": f"Test product {i}",
                            "price": f"{10 + i}.99",
                            "sku": f"TEST-{i:03d}"
                        },
                        emitted_at=1234567890 + i
                    )
                )
            )
        
        with patch('destination_shopify.destination.ShopifyClient') as mock_client_class:
            mock_client = Mock()
            mock_client.test_connection.return_value = True
            mock_client.get_shop_info.return_value = {"name": "Test Store"}
            mock_client.get_product_by_sku.return_value = None
            mock_client.create_product.return_value = {"id": "gid://shopify/Product/123", "title": "Test Product"}
            mock_client_class.return_value = mock_client
            
            # Execute write operation
            result_messages = list(destination.write(config, configured_catalog, messages))
            
            # Verify all products were created
            assert mock_client.create_product.call_count == 5

    def test_write_products_validation_error(self, config, configured_catalog):
        """Test handling of validation errors"""
        destination = DestinationShopify()
        
        # Message with missing required title field
        messages = [
            AirbyteMessage(
                type=Type.RECORD,
                record=AirbyteRecordMessage(
                    stream="products",
                    data={
                        "description": "A product without title",
                        "price": "29.99",
                        "sku": "TEST-001"
                    },
                    emitted_at=1234567890
                )
            )
        ]
        
        with patch('destination_shopify.destination.ShopifyClient') as mock_client_class:
            mock_client = Mock()
            mock_client.test_connection.return_value = True
            mock_client.get_shop_info.return_value = {"name": "Test Store"}
            mock_client_class.return_value = mock_client
            
            # Execute write operation - should handle validation error gracefully
            try:
                result_messages = list(destination.write(config, configured_catalog, messages))
                # Should not create any products due to validation error
                mock_client.create_product.assert_not_called()
            except Exception:
                # Expected to fail due to validation error
                pass
