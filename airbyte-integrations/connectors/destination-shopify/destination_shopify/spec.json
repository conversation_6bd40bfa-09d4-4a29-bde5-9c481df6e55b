{"documentationUrl": "https://docs.airbyte.com/integrations/destinations/shopify", "supported_destination_sync_modes": ["overwrite", "append"], "supportsIncremental": true, "connectionSpecification": {"$schema": "http://json-schema.org/draft-07/schema#", "title": "Shopify Destination Spec", "type": "object", "required": ["shop", "credentials"], "additionalProperties": true, "properties": {"shop": {"type": "string", "title": "Shopify Store", "description": "The name of your Shopify store found in the URL. For example, if your URL was https://NAME.myshopify.com, then the name would be 'NAME' or 'NAME.myshopify.com'.", "pattern": "^(?!https://)(?!https://).*", "examples": ["my-store", "my-store.myshopify.com"], "order": 1}, "credentials": {"title": "Shopify Authorization Method", "description": "The authorization method to use to write data to Shopify", "type": "object", "order": 2, "oneOf": [{"type": "object", "title": "OAuth2.0", "description": "OAuth2.0", "required": ["auth_method"], "properties": {"auth_method": {"type": "string", "const": "oauth2.0", "order": 0}, "client_id": {"type": "string", "title": "Client ID", "description": "The Client ID of the Shopify developer application.", "airbyte_secret": true, "order": 1}, "client_secret": {"type": "string", "title": "Client Secret", "description": "The Client Secret of the Shopify developer application.", "airbyte_secret": true, "order": 2}, "access_token": {"type": "string", "title": "Access Token", "description": "The Access Token for making authenticated requests.", "airbyte_secret": true, "order": 3}}}, {"title": "API Password", "description": "API Password Auth", "type": "object", "required": ["auth_method", "api_password"], "properties": {"auth_method": {"type": "string", "const": "api_password", "order": 0}, "api_password": {"type": "string", "title": "API Password", "description": "The API Password for your private application in the Shopify store.", "airbyte_secret": true, "order": 1}}}]}, "field_mapping": {"type": "object", "title": "Field Mapping Configuration", "description": "Configure how source fields map to Shopify product fields", "order": 3, "properties": {"product_title_field": {"type": "string", "title": "Product Title Field", "description": "Source field that maps to Shopify product title", "default": "title", "order": 1}, "product_description_field": {"type": "string", "title": "Product Description Field", "description": "Source field that maps to Shopify product description", "default": "description", "order": 2}, "product_vendor_field": {"type": "string", "title": "Product Vendor Field", "description": "Source field that maps to Shopify product vendor", "default": "vendor", "order": 3}, "product_type_field": {"type": "string", "title": "Product Type Field", "description": "Source field that maps to Shopify product type", "default": "product_type", "order": 4}, "images_field": {"type": "string", "title": "Images Field", "description": "Source field containing image URLs or file paths (comma-separated or JSON array)", "default": "images", "order": 5}, "videos_field": {"type": "string", "title": "Videos Field", "description": "Source field containing video URLs or file paths (comma-separated or JSON array)", "default": "videos", "order": 6}, "price_field": {"type": "string", "title": "Price Field", "description": "Source field that maps to Shopify product price", "default": "price", "order": 7}, "sku_field": {"type": "string", "title": "SKU Field", "description": "Source field that maps to Shopify product SKU", "default": "sku", "order": 8}, "inventory_quantity_field": {"type": "string", "title": "Inventory Quantity Field", "description": "Source field that maps to Shopify inventory quantity", "default": "inventory_quantity", "order": 9}, "tags_field": {"type": "string", "title": "Tags Field", "description": "Source field containing product tags (comma-separated or JSON array)", "default": "tags", "order": 10}}}, "media_upload_settings": {"type": "object", "title": "Media Upload Settings", "description": "Configure media upload behavior", "order": 4, "properties": {"upload_images": {"type": "boolean", "title": "Upload Images", "description": "Whether to upload images to Shopify", "default": true, "order": 1}, "upload_videos": {"type": "boolean", "title": "Upload Videos", "description": "Whether to upload videos to Shopify", "default": true, "order": 2}, "max_image_size_mb": {"type": "integer", "title": "Max Image Size (MB)", "description": "Maximum image file size in MB", "default": 20, "minimum": 1, "maximum": 100, "order": 3}, "max_video_size_mb": {"type": "integer", "title": "Max Video Size (MB)", "description": "Maximum video file size in MB", "default": 1024, "minimum": 1, "maximum": 5120, "order": 4}, "supported_image_formats": {"type": "array", "title": "Supported Image Formats", "description": "List of supported image formats", "default": ["jpg", "jpeg", "png", "gif", "webp"], "items": {"type": "string"}, "order": 5}, "supported_video_formats": {"type": "array", "title": "Supported Video Formats", "description": "List of supported video formats", "default": ["mp4", "mov", "avi", "webm"], "items": {"type": "string"}, "order": 6}}}, "sync_settings": {"type": "object", "title": "Sync Settings", "description": "Configure sync behavior", "order": 5, "properties": {"batch_size": {"type": "integer", "title": "<PERSON><PERSON> Si<PERSON>", "description": "Number of products to process in each batch", "default": 10, "minimum": 1, "maximum": 100, "order": 1}, "update_existing_products": {"type": "boolean", "title": "Update Existing Products", "description": "Whether to update existing products or only create new ones", "default": true, "order": 2}, "product_match_field": {"type": "string", "title": "Product Match Field", "description": "Field to use for matching existing products (sku, title, or handle)", "default": "sku", "enum": ["sku", "title", "handle"], "order": 3}}}}}}