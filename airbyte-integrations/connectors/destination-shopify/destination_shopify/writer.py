#
# Copyright (c) 2023 Airbyte, Inc., all rights reserved.
#

import logging
import time
from typing import Any, Dict, Iterable, List, Mapping, Optional

from airbyte_cdk.models import AirbyteMessage, AirbyteRecordMessage, Type

from .client import ShopifyClient, ShopifyAPIError
from .field_mapper import FieldMapper


class ShopifyWriter:
    """
    <PERSON>les writing data to Shopify
    """

    def __init__(self, config: Mapping[str, Any], client: ShopifyClient):
        self.config = config
        self.client = client
        self.field_mapper = FieldMapper(config.get("field_mapping", {}))
        self.sync_settings = config.get("sync_settings", {})
        self.media_settings = config.get("media_upload_settings", {})
        self.logger = logging.getLogger("airbyte")
        
        # Batch processing
        self.batch_size = self.sync_settings.get("batch_size", 10)
        self.current_batch = []
        
        # Statistics
        self.stats = {
            "products_created": 0,
            "products_updated": 0,
            "products_failed": 0,
            "media_uploaded": 0,
            "media_failed": 0
        }

    def write_records(self, records: Iterable[AirbyteRecordMessage]) -> None:
        """
        Write records to Shopify
        """
        for record in records:
            try:
                self.current_batch.append(record)
                
                if len(self.current_batch) >= self.batch_size:
                    self._process_batch()
                    self.current_batch = []
                    
            except Exception as e:
                self.logger.error(f"Failed to process record: {e}")
                self.stats["products_failed"] += 1

        # Process remaining records
        if self.current_batch:
            self._process_batch()

    def _process_batch(self) -> None:
        """
        Process a batch of records
        """
        self.logger.info(f"Processing batch of {len(self.current_batch)} records")
        
        for record in self.current_batch:
            try:
                self._process_single_record(record)
                # Add small delay to avoid rate limiting
                time.sleep(0.1)
            except Exception as e:
                self.logger.error(f"Failed to process record: {e}")
                self.stats["products_failed"] += 1

    def _process_single_record(self, record: AirbyteRecordMessage) -> None:
        """
        Process a single record
        """
        record_data = record.data
        
        # Validate required fields
        missing_fields = self.field_mapper.validate_required_fields(record_data)
        if missing_fields:
            raise ValueError(f"Missing required fields: {missing_fields}")

        # Map record to product data
        product_data = self.field_mapper.map_record_to_product(record_data)
        
        # Get media files
        media_files = self.field_mapper.get_media_files(record_data)
        
        # Check if product exists
        match_field = self.sync_settings.get("product_match_field", "sku")
        identifier = self.field_mapper.get_product_identifier(record_data, match_field)
        
        existing_product = None
        if identifier:
            if match_field == "sku":
                existing_product = self.client.get_product_by_sku(identifier)
            elif match_field == "title":
                existing_product = self.client.get_product_by_title(identifier)

        # Create or update product
        if existing_product and self.sync_settings.get("update_existing_products", True):
            product = self._update_product(existing_product["id"], product_data)
            self.stats["products_updated"] += 1
        else:
            product = self._create_product(product_data)
            self.stats["products_created"] += 1

        # Upload media if product was created/updated successfully
        if product and (media_files["images"] or media_files["videos"]):
            self._upload_product_media(product["id"], media_files)

    def _create_product(self, product_data: Dict[str, Any]) -> Optional[Dict]:
        """
        Create a new product
        """
        try:
            self.logger.info(f"Creating product: {product_data.get('title', 'Unknown')}")
            return self.client.create_product(product_data)
        except ShopifyAPIError as e:
            self.logger.error(f"Failed to create product: {e}")
            raise

    def _update_product(self, product_id: str, product_data: Dict[str, Any]) -> Optional[Dict]:
        """
        Update an existing product
        """
        try:
            self.logger.info(f"Updating product {product_id}: {product_data.get('title', 'Unknown')}")
            return self.client.update_product(product_id, product_data)
        except ShopifyAPIError as e:
            self.logger.error(f"Failed to update product {product_id}: {e}")
            raise

    def _upload_product_media(self, product_id: str, media_files: Dict[str, List[str]]) -> None:
        """
        Upload media files for a product
        """
        try:
            # Prepare media for upload
            prepared_media = self.field_mapper.prepare_media_for_upload(media_files, self.media_settings)
            
            if not prepared_media:
                return

            self.logger.info(f"Uploading {len(prepared_media)} media files for product {product_id}")
            
            # Upload files and create product media
            uploaded_files = []
            for media_item in prepared_media:
                try:
                    uploaded_file = self.client.upload_media_file(
                        media_item["originalSource"],
                        media_item.get("alt", ""),
                        media_item["mediaContentType"]
                    )
                    if uploaded_file:
                        uploaded_files.append({
                            "originalSource": media_item["originalSource"],
                            "mediaContentType": media_item["mediaContentType"],
                            "alt": media_item.get("alt", "")
                        })
                        self.stats["media_uploaded"] += 1
                except Exception as e:
                    self.logger.error(f"Failed to upload media file {media_item['originalSource']}: {e}")
                    self.stats["media_failed"] += 1

            # Create product media associations
            if uploaded_files:
                try:
                    self.client.create_product_media(product_id, uploaded_files)
                    self.logger.info(f"Successfully associated {len(uploaded_files)} media files with product {product_id}")
                except Exception as e:
                    self.logger.error(f"Failed to associate media with product {product_id}: {e}")

        except Exception as e:
            self.logger.error(f"Failed to upload media for product {product_id}: {e}")

    def get_stats(self) -> Dict[str, int]:
        """
        Get processing statistics
        """
        return self.stats.copy()

    def log_stats(self) -> None:
        """
        Log processing statistics
        """
        self.logger.info("Processing completed:")
        self.logger.info(f"  Products created: {self.stats['products_created']}")
        self.logger.info(f"  Products updated: {self.stats['products_updated']}")
        self.logger.info(f"  Products failed: {self.stats['products_failed']}")
        self.logger.info(f"  Media uploaded: {self.stats['media_uploaded']}")
        self.logger.info(f"  Media failed: {self.stats['media_failed']}")


class ShopifyBatchWriter:
    """
    Handles batch writing to Shopify with improved performance
    """

    def __init__(self, config: Mapping[str, Any], client: ShopifyClient):
        self.config = config
        self.client = client
        self.writer = ShopifyWriter(config, client)
        self.logger = logging.getLogger("airbyte")

    def write_messages(self, messages: Iterable[AirbyteMessage]) -> None:
        """
        Write Airbyte messages to Shopify
        """
        records = []
        
        for message in messages:
            if message.type == Type.RECORD:
                records.append(message.record)
            elif message.type == Type.STATE:
                # Process accumulated records before state
                if records:
                    self.writer.write_records(records)
                    records = []
                # Handle state message if needed
                self.logger.debug(f"Received state message: {message.state}")

        # Process remaining records
        if records:
            self.writer.write_records(records)

        # Log final statistics
        self.writer.log_stats()

    def get_stats(self) -> Dict[str, int]:
        """
        Get processing statistics
        """
        return self.writer.get_stats()
