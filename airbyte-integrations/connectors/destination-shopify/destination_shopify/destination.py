#
# Copyright (c) 2023 Airbyte, Inc., all rights reserved.
#

import logging
from typing import Any, Iterable, Mapping

from airbyte_cdk.destinations import Destination
from airbyte_cdk.models import AirbyteConnectionStatus, AirbyteMessage, ConfiguredAirbyteCatalog, Status

from .auth import MissingAccessTokenError, NotImplementedAuth
from .client import ShopifyClient, ShopifyAPIError
from .writer import ShopifyBatchWriter


class DestinationShopify(Destination):
    """
    Shopify destination connector for syncing data to Shopify stores
    """

    def write(
        self,
        config: Mapping[str, Any],
        configured_catalog: ConfiguredAirbyteCatalog,
        input_messages: Iterable[AirbyteMessage],
    ) -> Iterable[AirbyteMessage]:
        """
        Reads the input stream of messages, config, and catalog to write data to Shopify.

        :param config: dict of JSON configuration matching the configuration declared in spec.json
        :param configured_catalog: The Configured Catalog describing the schema of the data being received and how it should be persisted in the
                                    destination
        :param input_messages: The stream of input messages received from the source
        :return: Iterable of AirbyteStateMessages wrapped in AirbyteMessage structs
        """
        logger = logging.getLogger("airbyte")
        
        try:
            # Initialize client
            client = ShopifyClient(config)
            
            # Initialize writer
            writer = ShopifyBatchWriter(config, client)
            
            # Process messages
            writer.write_messages(input_messages)
            
            # Get final statistics
            stats = writer.get_stats()
            logger.info(f"Sync completed successfully. Stats: {stats}")
            
            # Yield state messages for successful processing
            for message in input_messages:
                if message.type == "STATE":
                    yield message
                    
        except Exception as e:
            logger.error(f"Sync failed: {e}")
            raise

    def check(self, logger: logging.Logger, config: Mapping[str, Any]) -> AirbyteConnectionStatus:
        """
        Tests if the input configuration can be used to successfully connect to the destination with the needed permissions
        e.g: if a provided API token or password can be used to connect and write to the destination.

        :param logger: Logging object to display debug/info/error to the logs
        :param config: Json object containing the configuration of this destination, content of this json is as specified in
        the properties of the spec.json file

        :return: AirbyteConnectionStatus indicating a Success or Failure
        """
        try:
            # Validate configuration
            validation_errors = self._validate_config(config)
            if validation_errors:
                return AirbyteConnectionStatus(
                    status=Status.FAILED,
                    message=f"Configuration validation failed: {'; '.join(validation_errors)}"
                )

            # Test connection to Shopify
            client = ShopifyClient(config)
            
            if not client.test_connection():
                return AirbyteConnectionStatus(
                    status=Status.FAILED,
                    message="Failed to connect to Shopify. Please check your credentials and shop URL."
                )

            # Test API permissions by getting shop info
            shop_info = client.get_shop_info()
            logger.info(f"Successfully connected to Shopify store: {shop_info.get('name', 'Unknown')}")

            return AirbyteConnectionStatus(status=Status.SUCCEEDED)

        except MissingAccessTokenError:
            return AirbyteConnectionStatus(
                status=Status.FAILED,
                message="Missing access token. Please provide a valid access token."
            )
        except NotImplementedAuth as e:
            return AirbyteConnectionStatus(
                status=Status.FAILED,
                message=f"Authentication method not supported: {e}"
            )
        except ShopifyAPIError as e:
            return AirbyteConnectionStatus(
                status=Status.FAILED,
                message=f"Shopify API error: {e}"
            )
        except Exception as e:
            logger.error(f"Connection check failed: {e}")
            return AirbyteConnectionStatus(
                status=Status.FAILED,
                message=f"Connection failed: {str(e)}"
            )

    def _validate_config(self, config: Mapping[str, Any]) -> list[str]:
        """
        Validate the configuration
        """
        errors = []

        # Check required fields
        if not config.get("shop"):
            errors.append("Shop URL is required")

        if not config.get("credentials"):
            errors.append("Credentials are required")
        else:
            credentials = config["credentials"]
            auth_method = credentials.get("auth_method")
            
            if auth_method == "oauth2.0":
                if not credentials.get("access_token"):
                    errors.append("Access token is required for OAuth2.0")
                if not credentials.get("client_id"):
                    errors.append("Client ID is required for OAuth2.0")
                if not credentials.get("client_secret"):
                    errors.append("Client secret is required for OAuth2.0")
            elif auth_method == "api_password":
                if not credentials.get("api_password"):
                    errors.append("API password is required for API password authentication")
            else:
                errors.append(f"Unsupported authentication method: {auth_method}")

        # Validate field mapping configuration
        field_mapping = config.get("field_mapping", {})
        if not field_mapping.get("product_title_field"):
            errors.append("Product title field mapping is required")

        # Validate sync settings
        sync_settings = config.get("sync_settings", {})
        batch_size = sync_settings.get("batch_size", 10)
        if not isinstance(batch_size, int) or batch_size < 1 or batch_size > 100:
            errors.append("Batch size must be an integer between 1 and 100")

        # Validate media upload settings
        media_settings = config.get("media_upload_settings", {})
        max_image_size = media_settings.get("max_image_size_mb", 20)
        if not isinstance(max_image_size, int) or max_image_size < 1 or max_image_size > 100:
            errors.append("Max image size must be an integer between 1 and 100 MB")

        max_video_size = media_settings.get("max_video_size_mb", 1024)
        if not isinstance(max_video_size, int) or max_video_size < 1 or max_video_size > 5120:
            errors.append("Max video size must be an integer between 1 and 5120 MB")

        return errors

    @staticmethod
    def get_shop_name(config: Mapping[str, Any]) -> str:
        """
        Extract shop name from configuration
        """
        shop = config.get("shop", "")
        split_pattern = ".myshopify.com"
        return shop.split(split_pattern)[0] if split_pattern in shop else shop
