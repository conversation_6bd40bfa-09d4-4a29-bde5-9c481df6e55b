#
# Copyright (c) 2023 Airbyte, Inc., all rights reserved.
#

import json
import logging
from typing import Any, Dict, List, Mapping, Optional, Union


class FieldMapper:
    """
    Maps source record fields to Shopify product fields based on configuration
    """

    def __init__(self, field_mapping_config: Mapping[str, Any]):
        self.config = field_mapping_config
        self.logger = logging.getLogger("airbyte")

    def map_record_to_product(self, record: Dict[str, Any]) -> Dict[str, Any]:
        """
        Map a source record to Shopify product format
        """
        product_data = {}
        
        # Map basic product fields
        if title := self._get_field_value(record, "product_title_field"):
            product_data["title"] = str(title)
            
        if description := self._get_field_value(record, "product_description_field"):
            product_data["descriptionHtml"] = str(description)
            
        if vendor := self._get_field_value(record, "product_vendor_field"):
            product_data["vendor"] = str(vendor)
            
        if product_type := self._get_field_value(record, "product_type_field"):
            product_data["productType"] = str(product_type)
            
        # Map tags
        if tags := self._get_field_value(record, "tags_field"):
            product_data["tags"] = self._parse_tags(tags)
            
        # Map variant data (price, SKU, inventory)
        variant_data = {}
        
        if price := self._get_field_value(record, "price_field"):
            variant_data["price"] = str(price)
            
        if sku := self._get_field_value(record, "sku_field"):
            variant_data["sku"] = str(sku)
            
        if inventory_quantity := self._get_field_value(record, "inventory_quantity_field"):
            variant_data["inventoryQuantities"] = [{
                "availableQuantity": int(inventory_quantity),
                "locationId": "gid://shopify/Location/main"  # Default location
            }]
            
        if variant_data:
            product_data["variants"] = [variant_data]
            
        return product_data

    def get_media_files(self, record: Dict[str, Any]) -> Dict[str, List[str]]:
        """
        Extract media files (images and videos) from record
        """
        media_files = {"images": [], "videos": []}
        
        # Get images
        if images := self._get_field_value(record, "images_field"):
            media_files["images"] = self._parse_media_urls(images)
            
        # Get videos
        if videos := self._get_field_value(record, "videos_field"):
            media_files["videos"] = self._parse_media_urls(videos)
            
        return media_files

    def get_product_identifier(self, record: Dict[str, Any], match_field: str = "sku") -> Optional[str]:
        """
        Get the identifier to match existing products
        """
        if match_field == "sku":
            return self._get_field_value(record, "sku_field")
        elif match_field == "title":
            return self._get_field_value(record, "product_title_field")
        elif match_field == "handle":
            # Generate handle from title if available
            if title := self._get_field_value(record, "product_title_field"):
                return self._generate_handle(title)
        return None

    def _get_field_value(self, record: Dict[str, Any], config_key: str) -> Optional[Any]:
        """
        Get field value from record based on configuration
        """
        field_name = self.config.get(config_key)
        if not field_name:
            return None
            
        # Support nested field access with dot notation
        if "." in field_name:
            return self._get_nested_value(record, field_name.split("."))
        
        return record.get(field_name)

    def _get_nested_value(self, data: Dict[str, Any], keys: List[str]) -> Optional[Any]:
        """
        Get nested value from dictionary using list of keys
        """
        current = data
        for key in keys:
            if isinstance(current, dict) and key in current:
                current = current[key]
            else:
                return None
        return current

    def _parse_tags(self, tags: Any) -> List[str]:
        """
        Parse tags from various formats (string, list, JSON)
        """
        if isinstance(tags, list):
            return [str(tag).strip() for tag in tags if tag]
        elif isinstance(tags, str):
            try:
                # Try to parse as JSON array
                parsed = json.loads(tags)
                if isinstance(parsed, list):
                    return [str(tag).strip() for tag in parsed if tag]
            except json.JSONDecodeError:
                pass
            
            # Parse as comma-separated string
            return [tag.strip() for tag in tags.split(",") if tag.strip()]
        
        return []

    def _parse_media_urls(self, media: Any) -> List[str]:
        """
        Parse media URLs from various formats (string, list, JSON)
        """
        if isinstance(media, list):
            return [str(url).strip() for url in media if url and str(url).strip()]
        elif isinstance(media, str):
            try:
                # Try to parse as JSON array
                parsed = json.loads(media)
                if isinstance(parsed, list):
                    return [str(url).strip() for url in parsed if url and str(url).strip()]
            except json.JSONDecodeError:
                pass
            
            # Parse as comma-separated string
            return [url.strip() for url in media.split(",") if url.strip()]
        
        return []

    def _generate_handle(self, title: str) -> str:
        """
        Generate Shopify handle from product title
        """
        import re
        
        # Convert to lowercase and replace spaces/special chars with hyphens
        handle = re.sub(r'[^a-zA-Z0-9\s-]', '', title.lower())
        handle = re.sub(r'\s+', '-', handle)
        handle = re.sub(r'-+', '-', handle)
        handle = handle.strip('-')
        
        return handle

    def validate_required_fields(self, record: Dict[str, Any]) -> List[str]:
        """
        Validate that required fields are present in the record
        """
        missing_fields = []
        
        # Check for required product title
        if not self._get_field_value(record, "product_title_field"):
            missing_fields.append("product_title_field")
            
        return missing_fields

    def prepare_media_for_upload(self, media_files: Dict[str, List[str]], 
                                media_settings: Mapping[str, Any]) -> List[Dict[str, Any]]:
        """
        Prepare media files for upload based on settings
        """
        prepared_media = []
        
        # Process images
        if media_settings.get("upload_images", True) and media_files.get("images"):
            for image_url in media_files["images"]:
                if self._is_valid_media_url(image_url, "image", media_settings):
                    prepared_media.append({
                        "originalSource": image_url,
                        "mediaContentType": "IMAGE",
                        "alt": ""
                    })
                    
        # Process videos
        if media_settings.get("upload_videos", True) and media_files.get("videos"):
            for video_url in media_files["videos"]:
                if self._is_valid_media_url(video_url, "video", media_settings):
                    prepared_media.append({
                        "originalSource": video_url,
                        "mediaContentType": "VIDEO",
                        "alt": ""
                    })
                    
        return prepared_media

    def _is_valid_media_url(self, url: str, media_type: str, settings: Mapping[str, Any]) -> bool:
        """
        Validate media URL based on settings
        """
        if not url or not url.startswith(("http://", "https://")):
            return False
            
        # Check file extension
        url_lower = url.lower()
        if media_type == "image":
            supported_formats = settings.get("supported_image_formats", ["jpg", "jpeg", "png", "gif", "webp"])
            return any(url_lower.endswith(f".{fmt}") for fmt in supported_formats)
        elif media_type == "video":
            supported_formats = settings.get("supported_video_formats", ["mp4", "mov", "avi", "webm"])
            return any(url_lower.endswith(f".{fmt}") for fmt in supported_formats)
            
        return False
