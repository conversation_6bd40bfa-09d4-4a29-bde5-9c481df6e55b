#
# Copyright (c) 2023 Airbyte, Inc., all rights reserved.
#

import json
import logging
import time
from typing import Any, Dict, List, Mapping, Optional, Union
from urllib.parse import urljoin

import requests
from requests.adapters import HTTPAdapter
from urllib3.util.retry import Retry

from .auth import ShopifyAuthenticator


class ShopifyAPIError(Exception):
    """Custom exception for Shopify API errors"""
    pass


class ShopifyClient:
    """
    Client for interacting with Shopify REST and GraphQL APIs
    """

    def __init__(self, config: Mapping[str, Any]):
        self.config = config
        self.shop = self._get_shop_name(config.get("shop"))
        self.authenticator = ShopifyAuthenticator(config)
        self.logger = logging.getLogger("airbyte")
        
        # Set up session with retry strategy
        self.session = requests.Session()
        retry_strategy = Retry(
            total=3,
            status_forcelist=[429, 500, 502, 503, 504],
            method_whitelist=["HEAD", "GET", "PUT", "DELETE", "OPTIONS", "TRACE", "POST"],
            backoff_factor=1
        )
        adapter = HTTPAdapter(max_retries=retry_strategy)
        self.session.mount("http://", adapter)
        self.session.mount("https://", adapter)
        
        # API endpoints
        self.base_url = f"https://{self.shop}.myshopify.com"
        self.rest_api_url = f"{self.base_url}/admin/api/2023-10"
        self.graphql_url = f"{self.base_url}/admin/api/2023-10/graphql.json"

    @staticmethod
    def _get_shop_name(shop: str) -> str:
        """Extract shop name from URL or return as-is"""
        split_pattern = ".myshopify.com"
        return shop.split(split_pattern)[0] if split_pattern in shop else shop

    def _make_request(self, method: str, url: str, **kwargs) -> requests.Response:
        """Make HTTP request with authentication and error handling"""
        headers = kwargs.pop("headers", {})
        headers.update(self.authenticator.get_request_headers())
        
        try:
            response = self.session.request(method, url, headers=headers, **kwargs)
            
            # Handle rate limiting
            if response.status_code == 429:
                retry_after = int(response.headers.get("Retry-After", 1))
                self.logger.warning(f"Rate limited. Waiting {retry_after} seconds...")
                time.sleep(retry_after)
                return self._make_request(method, url, headers=headers, **kwargs)
            
            response.raise_for_status()
            return response
            
        except requests.exceptions.RequestException as e:
            self.logger.error(f"Request failed: {e}")
            raise ShopifyAPIError(f"API request failed: {e}")

    def _make_graphql_request(self, query: str, variables: Optional[Dict] = None) -> Dict:
        """Make GraphQL request"""
        headers = self.authenticator.get_graphql_headers()
        payload = {"query": query}
        if variables:
            payload["variables"] = variables
            
        response = self._make_request("POST", self.graphql_url, json=payload, headers=headers)
        data = response.json()
        
        if "errors" in data:
            error_msg = "; ".join([error.get("message", str(error)) for error in data["errors"]])
            raise ShopifyAPIError(f"GraphQL error: {error_msg}")
            
        return data

    def test_connection(self) -> bool:
        """Test the connection to Shopify"""
        try:
            url = f"{self.rest_api_url}/shop.json"
            response = self._make_request("GET", url)
            return response.status_code == 200
        except Exception as e:
            self.logger.error(f"Connection test failed: {e}")
            return False

    def get_product_by_sku(self, sku: str) -> Optional[Dict]:
        """Get product by SKU"""
        try:
            query = """
            query getProductBySku($query: String!) {
                products(first: 1, query: $query) {
                    edges {
                        node {
                            id
                            handle
                            title
                            variants(first: 1) {
                                edges {
                                    node {
                                        id
                                        sku
                                    }
                                }
                            }
                        }
                    }
                }
            }
            """
            variables = {"query": f"sku:{sku}"}
            response = self._make_graphql_request(query, variables)
            
            products = response.get("data", {}).get("products", {}).get("edges", [])
            return products[0]["node"] if products else None
            
        except Exception as e:
            self.logger.error(f"Failed to get product by SKU {sku}: {e}")
            return None

    def get_product_by_title(self, title: str) -> Optional[Dict]:
        """Get product by title"""
        try:
            query = """
            query getProductByTitle($query: String!) {
                products(first: 1, query: $query) {
                    edges {
                        node {
                            id
                            handle
                            title
                        }
                    }
                }
            }
            """
            variables = {"query": f"title:{title}"}
            response = self._make_graphql_request(query, variables)
            
            products = response.get("data", {}).get("products", {}).get("edges", [])
            return products[0]["node"] if products else None
            
        except Exception as e:
            self.logger.error(f"Failed to get product by title {title}: {e}")
            return None

    def create_product(self, product_data: Dict) -> Optional[Dict]:
        """Create a new product"""
        try:
            mutation = """
            mutation productCreate($input: ProductInput!) {
                productCreate(input: $input) {
                    product {
                        id
                        handle
                        title
                    }
                    userErrors {
                        field
                        message
                    }
                }
            }
            """
            variables = {"input": product_data}
            response = self._make_graphql_request(mutation, variables)
            
            result = response.get("data", {}).get("productCreate", {})
            if result.get("userErrors"):
                errors = "; ".join([f"{err['field']}: {err['message']}" for err in result["userErrors"]])
                raise ShopifyAPIError(f"Product creation failed: {errors}")
                
            return result.get("product")
            
        except Exception as e:
            self.logger.error(f"Failed to create product: {e}")
            raise

    def update_product(self, product_id: str, product_data: Dict) -> Optional[Dict]:
        """Update an existing product"""
        try:
            mutation = """
            mutation productUpdate($input: ProductInput!) {
                productUpdate(input: $input) {
                    product {
                        id
                        handle
                        title
                    }
                    userErrors {
                        field
                        message
                    }
                }
            }
            """
            product_data["id"] = product_id
            variables = {"input": product_data}
            response = self._make_graphql_request(mutation, variables)
            
            result = response.get("data", {}).get("productUpdate", {})
            if result.get("userErrors"):
                errors = "; ".join([f"{err['field']}: {err['message']}" for err in result["userErrors"]])
                raise ShopifyAPIError(f"Product update failed: {errors}")
                
            return result.get("product")
            
        except Exception as e:
            self.logger.error(f"Failed to update product {product_id}: {e}")
            raise

    def upload_media_file(self, file_url: str, alt_text: str = "", media_type: str = "IMAGE") -> Optional[Dict]:
        """Upload media file from URL"""
        try:
            mutation = """
            mutation fileCreate($files: [FileCreateInput!]!) {
                fileCreate(files: $files) {
                    files {
                        id
                        fileStatus
                        preview {
                            image {
                                url
                            }
                        }
                    }
                    userErrors {
                        field
                        message
                    }
                }
            }
            """

            file_input = {
                "originalSource": file_url,
                "contentType": media_type,
                "alt": alt_text
            }

            variables = {"files": [file_input]}
            response = self._make_graphql_request(mutation, variables)

            result = response.get("data", {}).get("fileCreate", {})
            if result.get("userErrors"):
                errors = "; ".join([f"{err['field']}: {err['message']}" for err in result["userErrors"]])
                raise ShopifyAPIError(f"Media upload failed: {errors}")

            files = result.get("files", [])
            return files[0] if files else None

        except Exception as e:
            self.logger.error(f"Failed to upload media file {file_url}: {e}")
            raise

    def create_product_media(self, product_id: str, media_files: List[Dict]) -> List[Dict]:
        """Create product media from uploaded files"""
        try:
            mutation = """
            mutation productCreateMedia($productId: ID!, $media: [CreateMediaInput!]!) {
                productCreateMedia(productId: $productId, media: $media) {
                    media {
                        id
                        mediaContentType
                        status
                    }
                    mediaUserErrors {
                        field
                        message
                    }
                }
            }
            """

            variables = {
                "productId": product_id,
                "media": media_files
            }
            response = self._make_graphql_request(mutation, variables)

            result = response.get("data", {}).get("productCreateMedia", {})
            if result.get("mediaUserErrors"):
                errors = "; ".join([f"{err['field']}: {err['message']}" for err in result["mediaUserErrors"]])
                raise ShopifyAPIError(f"Product media creation failed: {errors}")

            return result.get("media", [])

        except Exception as e:
            self.logger.error(f"Failed to create product media for {product_id}: {e}")
            raise

    def get_shop_info(self) -> Dict:
        """Get shop information"""
        try:
            url = f"{self.rest_api_url}/shop.json"
            response = self._make_request("GET", url)
            return response.json().get("shop", {})
        except Exception as e:
            self.logger.error(f"Failed to get shop info: {e}")
            raise
