#!/usr/bin/env python3

"""
Simple test script to verify the Shopify destination connector structure
"""

import sys
import os

# Add the current directory to Python path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def test_imports():
    """Test that all modules can be imported"""
    try:
        from destination_shopify.destination import DestinationShopify
        print("✓ DestinationShopify imported successfully")
        
        from destination_shopify.client import ShopifyClient
        print("✓ ShopifyClient imported successfully")
        
        from destination_shopify.auth import ShopifyAuthenticator
        print("✓ ShopifyAuthenticator imported successfully")
        
        from destination_shopify.field_mapper import FieldMapper
        print("✓ FieldMapper imported successfully")
        
        from destination_shopify.writer import ShopifyWriter, ShopifyBatchWriter
        print("✓ ShopifyWriter and ShopifyBatchWriter imported successfully")
        
        return True
    except Exception as e:
        print(f"✗ Import failed: {e}")
        return False

def test_destination_instantiation():
    """Test that the destination can be instantiated"""
    try:
        from destination_shopify.destination import DestinationShopify
        destination = DestinationShopify()
        print("✓ DestinationShopify instantiated successfully")
        return True
    except Exception as e:
        print(f"✗ Destination instantiation failed: {e}")
        return False

def test_spec_loading():
    """Test that the spec can be loaded"""
    try:
        import json
        with open("destination_shopify/spec.json", "r") as f:
            spec = json.load(f)
        print("✓ Spec.json loaded successfully")
        print(f"  - Connection specification has {len(spec['connectionSpecification']['properties'])} properties")
        return True
    except Exception as e:
        print(f"✗ Spec loading failed: {e}")
        return False

def main():
    """Run all tests"""
    print("Testing Shopify Destination Connector...")
    print("=" * 50)
    
    tests = [
        test_imports,
        test_destination_instantiation,
        test_spec_loading
    ]
    
    passed = 0
    total = len(tests)
    
    for test in tests:
        if test():
            passed += 1
        print()
    
    print("=" * 50)
    print(f"Tests passed: {passed}/{total}")
    
    if passed == total:
        print("🎉 All tests passed! The connector structure is working correctly.")
        return 0
    else:
        print("❌ Some tests failed. Please check the errors above.")
        return 1

if __name__ == "__main__":
    sys.exit(main())
