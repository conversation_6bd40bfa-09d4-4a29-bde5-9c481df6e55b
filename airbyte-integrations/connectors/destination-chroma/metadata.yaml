data:
  registryOverrides:
    oss:
      enabled: true
    cloud:
      enabled: false
  connectorBuildOptions:
    baseImage: docker.io/airbyte/python-connector-base:4.0.0@sha256:d9894b6895923b379f3006fa251147806919c62b7d9021b5cd125bb67d7bbe22
  connectorSubtype: vectorstore
  connectorType: destination
  definitionId: 0b75218b-f702-4a28-85ac-34d3d84c0fc2
  dockerImageTag: 0.0.54
  dockerRepository: airbyte/destination-chroma
  githubIssueLabel: destination-chroma
  icon: chroma.svg
  license: ELv2
  name: Chroma
  releaseDate: "2023-09-13"
  releaseStage: alpha
  supportLevel: community
  documentationUrl: https://docs.airbyte.com/integrations/destinations/chroma
  tags:
    - language:python
    - cdk:python
  connectorTestSuitesOptions:
    - suite: unitTests
    - suite: integrationTests
metadataSpecVersion: "1.0"
